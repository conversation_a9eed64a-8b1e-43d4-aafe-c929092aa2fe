# ESAdok PDF Server

This is a [Next.js](https://nextjs.org) project bootstrapped with [`create-next-app`](https://nextjs.org/docs/pages/api-reference/create-next-app).

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `pages/index.js`. The page auto-updates as you edit the file.

[API routes](https://nextjs.org/docs/pages/building-your-application/routing/api-routes) can be accessed on [http://localhost:3000/api/hello](http://localhost:3000/api/hello). This endpoint can be edited in `pages/api/hello.js`.

The `pages/api` directory is mapped to `/api/*`. Files in this directory are treated as [API routes](https://nextjs.org/docs/pages/building-your-application/routing/api-routes) instead of React pages.

This project uses [`next/font`](https://nextjs.org/docs/pages/building-your-application/optimizing/fonts) to automatically optimize and load [<PERSON><PERSON><PERSON>](https://vercel.com/font), a new font family for Vercel.

## API Usage

This application is part of the ELKE system (Elektronische Kontrolle) for managing animal welfare controls in Austria. It provides a REST API for managing PDF documents related to animal welfare inspections.

### Database Structure

The system uses a relational database with the following key components:

- **PDF Documents**: Each uploaded PDF is stored with metadata including filename, upload timestamp, and file size
- **BKB Identifiers**: "Betreuungs- oder Kontrollbesuch" (Care or Control Visit) identifiers follow a structured format (e.g., `ELK.KB-TEST.2025.DUMMY123`)
  - These identifiers are used to track animal welfare inspection visits and controls
  - Part of Austria's official animal welfare monitoring system
- **UUID v7 Identification**: Each PDF file is assigned a version 7 UUID (time-ordered) for safe and unique identification
  - v7 UUIDs provide chronological ordering while maintaining uniqueness
  - This ensures safe concurrent operations and efficient database indexing
  - The timestamp component allows for natural sorting by creation time

### Upload PDF

Upload a PDF file with BKB identifier:

```bash
curl -X POST http://localhost:3000/api/v1/uploadPDF \
  -F "pdf=@/path/to/your/file.pdf" \
  -F "bkb=ELK.KB-TEST.2025.DUMMY123"
```

**Response**: Returns a JSON object containing the assigned UUID v7 identifier and upload confirmation.

## Deploy on Windows Server

To deploy this Next.js application on a custom Windows server, follow these steps:

### Prerequisites

1. **Node.js**: Install Node.js (version 18 or higher) from [nodejs.org](https://nodejs.org/)
2. **IIS (Internet Information Services)**: Enable IIS with the following features:
   - Web Server (IIS)
   - Application Development Features
   - CGI
3. **iisnode**: Install the [iisnode module](https://github.com/Azure/iisnode) for IIS

### Deployment Steps

1. **Build the application**:
   ```bash
   npm run build
   ```

2. **Create deployment directory**:
   Create a folder on your Windows server (e.g., `C:\inetpub\wwwroot\esadok`)

3. **Copy files**:
   Copy the following to your deployment directory:
   - `.next` folder (build output)
   - `public` folder
   - `package.json`
   - `next.config.js` (if exists)
   - `node_modules` folder (or run `npm install --production` on the server)

4. **Configure IIS**:
   - Create a new website in IIS Manager
   - Set the physical path to your deployment directory
   - Create a `web.config` file in the root directory:

   ```xml
   <?xml version="1.0" encoding="utf-8"?>
   <configuration>
     <system.webServer>
       <handlers>
         <add name="iisnode" path="server.js" verb="*" modules="iisnode"/>
       </handlers>
       <rewrite>
         <rules>
           <rule name="NodeInspector" patternSyntax="ECMAScript" stopProcessing="true">
             <match url="^server.js\/debug[\/]?" />
           </rule>
           <rule name="StaticContent">
             <action type="Rewrite" url="public{REQUEST_URI}"/>
           </rule>
           <rule name="DynamicContent">
             <conditions>
               <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="True"/>
             </conditions>
             <action type="Rewrite" url="server.js"/>
           </rule>
         </rules>
       </rewrite>
       <security>
         <requestFiltering>
           <hiddenSegments>
             <remove segment="bin"/>
           </hiddenSegments>
         </requestFiltering>
       </security>
       <httpErrors existingResponse="PassThrough" />
     </system.webServer>
   </configuration>
   ```

5. **Create server.js**:
   Create a `server.js` file in the root directory:
   ```javascript
   const { createServer } = require('http')
   const { parse } = require('url')
   const next = require('next')

   const dev = process.env.NODE_ENV !== 'production'
   const hostname = 'localhost'
   const port = process.env.PORT || 3000

   const app = next({ dev, hostname, port })
   const handle = app.getRequestHandler()

   app.prepare().then(() => {
     createServer(async (req, res) => {
       try {
         const parsedUrl = parse(req.url, true)
         await handle(req, res, parsedUrl)
       } catch (err) {
         console.error('Error occurred handling', req.url, err)
         res.statusCode = 500
         res.end('internal server error')
       }
     }).listen(port, (err) => {
       if (err) throw err
       console.log(`> Ready on http://${hostname}:${port}`)
     })
   })
   ```

6. **Set environment variables**:
   Configure environment variables in IIS or through `iisnode.yml`:
   ```yaml
   nodeProcessCommandLine: "C:\Program Files\nodejs\node.exe"
   interceptor: "C:\Program Files\iisnode\interceptor.js"
   ```

7. **Start the application**:
   The application should now be accessible through your IIS website.

### Troubleshooting

- Check IIS logs and Node.js logs for any errors
- Ensure proper file permissions for the IIS application pool user
- Verify that all required Node.js modules are installed
- Test the application locally before deploying to the server
