import Database from 'better-sqlite3';

const db = new Database('ESAdok.sqlite');

// Haupttabelle für Kontrollberichte
db.prepare(`
  CREATE TABLE IF NOT EXISTS Kontrollberichte (
    id VARCHAR(26) PRIMARY KEY,
    bkb TEXT NOT NULL,
    data BLOB NOT NULL,
    original_filename TEXT,
    file_size INTEGER,
    uploaded_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
  )
`).run();

// Tabelle für Download-Tokens
db.prepare(`
  CREATE TABLE IF NOT EXISTS download_tokens (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    kontrollbericht_id VARCHAR(26) NOT NULL,
    token VARCHAR(64) NOT NULL UNIQUE,
    lfbis_number TEXT NOT NULL,
    email TEXT NOT NULL,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    expires_at DATETIME NOT NULL,
    used_at DATETIME,
    FOREIGN KEY (kontrollbericht_id) REFERENCES Kontrollberichte(id)
  )
`).run();

// Funktion zum Öffnen der Datenbank
export function openDb() {
  return db;
}

export default db;
