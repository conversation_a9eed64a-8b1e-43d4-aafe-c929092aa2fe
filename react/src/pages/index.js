import Head from "next/head";

export default function Home() {
  return (
    <>
      <Head>
        <title>ESAdok Server</title>
        <meta name="description" content="ESAdok PDF Server - ELKE System for Animal Welfare Controls in Austria" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
        <link
          href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css"
          rel="stylesheet"
          integrity="sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN"
          crossOrigin="anonymous"
        />
      </Head>

      <div className="min-vh-100 bg-light">
        {/* Navigation */}
        <nav className="navbar navbar-expand-lg navbar-dark bg-primary shadow-sm">
          <div className="container">
            <span className="navbar-brand mb-0 h1">
              <i className="bi bi-file-earmark-pdf me-2"></i>
              ESAdok Server
            </span>
          </div>
        </nav>

        {/* Main Content */}
        <div className="container-fluid">
          <div className="row justify-content-center align-items-center" style={{ minHeight: 'calc(100vh - 76px)' }}>
            <div className="col-md-8 col-lg-6">
              <div className="card shadow-lg border-0">
                <div className="card-body text-center p-5">
                  <div className="mb-4">
                    <i className="bi bi-server text-primary" style={{ fontSize: '4rem' }}></i>
                  </div>

                  <h1 className="card-title display-4 fw-bold text-primary mb-3">
                    ESAdok Server
                  </h1>

                  <p className="card-text lead text-muted mb-4">
                    ELKE System für tierschutzrelevante Kontrollen in Österreich
                  </p>

                  <div className="row text-center mt-4">
                    <div className="col-md-4 mb-3">
                      <div className="p-3">
                        <i className="bi bi-file-earmark-pdf text-success" style={{ fontSize: '2rem' }}></i>
                        <h6 className="mt-2 text-muted">PDF Management</h6>
                      </div>
                    </div>
                    <div className="col-md-4 mb-3">
                      <div className="p-3">
                        <i className="bi bi-shield-check text-info" style={{ fontSize: '2rem' }}></i>
                        <h6 className="mt-2 text-muted">Tierschutzkontrollen</h6>
                      </div>
                    </div>
                    <div className="col-md-4 mb-3">
                      <div className="p-3">
                        <i className="bi bi-database text-warning" style={{ fontSize: '2rem' }}></i>
                        <h6 className="mt-2 text-muted">Secure Storage</h6>
                      </div>
                    </div>
                  </div>

                  <div className="mt-4 pt-3 border-top">
                    <small className="text-muted">
                      <i className="bi bi-info-circle me-1"></i>
                      Server läuft und bereit für API-Anfragen
                    </small>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <footer className="bg-dark text-light py-3 mt-auto">
          <div className="container text-center">
            <small>
              © 2025 ESAdok Server - ELKE System
            </small>
          </div>
        </footer>
      </div>

      {/* Bootstrap Icons */}
      <link
        rel="stylesheet"
        href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css"
      />
    </>
  );
}
