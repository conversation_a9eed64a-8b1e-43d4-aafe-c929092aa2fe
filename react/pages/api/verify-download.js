import fs from 'fs';
import path from 'path';
import { openDb } from '../../lib/db';

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  const { token, lfbis, email } = req.body;

  if (!token || !lfbis || !email) {
    return res.status(400).json({ message: 'Alle Felder sind erforderlich.' });
  }

  try {
    const db = openDb();

    // Token validieren und Kontrollbericht-Daten abrufen
    const stmt = db.prepare(`
      SELECT k.id, k.data, k.original_filename, k.bkb
      FROM download_tokens dt
      JOIN Kontrollberichte k ON dt.kontrollbericht_id = k.id
      WHERE dt.token = ?
      AND dt.lfbis_number = ?
      AND dt.email = ?
      AND dt.expires_at > datetime('now')
      AND dt.used_at IS NULL
    `);

    const result = stmt.get(token, lfbis, email);

    if (!result) {
      return res.status(403).json({
        message: 'Ungültiger Token oder falsche Daten.'
      });
    }

    // Token als verwendet markieren
    const updateStmt = db.prepare(`
      UPDATE download_tokens
      SET used_at = datetime('now')
      WHERE token = ?
    `);
    updateStmt.run(token);

    // PDF-Daten direkt aus der Datenbank senden
    const pdfBuffer = result.data;
    const filename = result.original_filename || `Kontrollbericht_${result.bkb}.pdf`;

    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    res.setHeader('Content-Length', pdfBuffer.length);

    res.send(pdfBuffer);

  } catch (error) {
    console.error('Download error:', error);
    res.status(500).json({ 
      message: 'Serverfehler beim Download.' 
    });
  }
}
