import multer from 'multer';
import nextConnect from 'next-connect';
import db from '../../../lib/db.js';
import { uuidv7 } from 'uuidv7';

const upload = multer({ storage: multer.memoryStorage() });

const apiRoute = nextConnect();

apiRoute.use(upload.single('pdf'));

apiRoute.post((req, res) => {
    const file = req.file;
    const bkb = req.body.bkb;

    if (!file || !bkb) {
        return res.status(400).json({ error: 'PDF und BKB müssen übermittelt werden' });
    }

    const id = uuidv7();

    const stmt = db.prepare(`
    INSERT INTO Kontrollberichte (id, bkb, data)
    VALUES (?, ?, ?)
  `);

    stmt.run(id, bkb, file.buffer);

    res.status(200).json({ message: 'PDF erfolgreich gespeichert', id });
});

export const config = {
    api: {
        bodyParser: false,
    },
};

export default apiRoute;
