import Head from "next/head";
import { useState, useEffect } from "react";
import { useRouter } from "next/router";

export default function Download() {
  const router = useRouter();
  const [formData, setFormData] = useState({
    token: '',
    lfbis: '',
    email: ''
  });
  const [message, setMessage] = useState({ type: '', text: '', show: false });

  useEffect(() => {
    // Token aus URL lesen
    const { token } = router.query;
    if (token) {
      setFormData(prev => ({ ...prev, token: token }));
    }
  }, [router.query]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const showMessage = (type, text) => {
    setMessage({ type, text, show: true });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setMessage({ type: '', text: '', show: false });

    try {
      const response = await fetch('/api/verify-download', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData)
      });

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'Kontrollbericht.pdf';
        document.body.appendChild(a);
        a.click();
        a.remove();
        window.URL.revokeObjectURL(url);
      } else {
        const err = await response.json();
        showMessage('danger', err.message || 'Zugriff verweigert.');
      }
    } catch (err) {
      showMessage('danger', 'Fehler beim Serverzugriff.');
    }
  };

  return (
    <>
      <Head>
        <title>PDF-Download – ESAdok</title>
        <meta name="description" content="Kontrollbericht herunterladen - ESAdok Server" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <style jsx>{`
        body {
          background-color: #f8f9fa;
        }
        .download-box {
          max-width: 500px;
          margin: 5rem auto;
          padding: 2rem;
          background-color: white;
          border-radius: 0.5rem;
          box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
      `}</style>

      <div className="download-box">
        <h4 className="mb-3 text-center">Kontrollbericht herunterladen</h4>
        <p className="text-muted text-center">
          Bitte geben Sie Ihre Daten ein, um das Dokument abzurufen.
        </p>

        <form onSubmit={handleSubmit}>
          <div className="mb-3">
            <label htmlFor="lfbis" className="form-label">LFBIS-Nummer</label>
            <input
              type="text"
              className="form-control"
              id="lfbis"
              name="lfbis"
              value={formData.lfbis}
              onChange={handleInputChange}
              required
            />
          </div>
          <div className="mb-3">
            <label htmlFor="email" className="form-label">E-Mail-Adresse</label>
            <input
              type="email"
              className="form-control"
              id="email"
              name="email"
              value={formData.email}
              onChange={handleInputChange}
              required
            />
          </div>
          <input type="hidden" name="token" value={formData.token} />
          <button type="submit" className="btn btn-primary w-100">
            Download starten
          </button>
        </form>

        {message.show && (
          <div className={`alert alert-${message.type} mt-3`} role="alert">
            {message.text}
          </div>
        )}
      </div>
    </>
  );
}
