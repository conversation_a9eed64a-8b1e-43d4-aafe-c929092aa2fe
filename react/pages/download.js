import { useState, useEffect } from "react";
import { useRouter } from "next/router";
import Layout from "./components/Layout";

export default function Download() {
  const router = useRouter();
  const [formData, setFormData] = useState({
    token: '',
    lfbis: '',
    email: ''
  });
  const [message, setMessage] = useState({ type: '', text: '', show: false });

  useEffect(() => {
    // Token aus URL lesen
    const { token } = router.query;
    if (token) {
      setFormData(prev => ({ ...prev, token: token }));
    }
  }, [router.query]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const showMessage = (type, text) => {
    setMessage({ type, text, show: true });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setMessage({ type: '', text: '', show: false });

    try {
      const response = await fetch('/api/verify-download', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData)
      });

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'Kontrollbericht.pdf';
        document.body.appendChild(a);
        a.click();
        a.remove();
        window.URL.revokeObjectURL(url);
        showMessage('success', 'Download erfolgreich gestartet!');
      } else {
        const err = await response.json();
        showMessage('danger', err.message || 'Zugriff verweigert.');
      }
    } catch (err) {
      showMessage('danger', 'Fehler beim Serverzugriff.');
    }
  };

  return (
    <Layout
      title="PDF-Download – ESAdok"
      description="Kontrollbericht herunterladen - ESAdok Server"
    >
      <div className="row justify-content-center">
        <div className="col-md-6 col-lg-5">
          <div className="card shadow-lg border-0">
            <div className="card-body p-4">
              <div className="text-center mb-4">
                <i className="bi bi-download text-primary" style={{ fontSize: '3rem' }}></i>
                <h4 className="mt-3 mb-2">Kontrollbericht herunterladen</h4>
                <p className="text-muted">
                  Bitte geben Sie Ihre Daten ein, um das Dokument abzurufen.
                </p>
              </div>

              <form onSubmit={handleSubmit}>
                <div className="mb-3">
                  <label htmlFor="lfbis" className="form-label">
                    <i className="bi bi-building me-2"></i>
                    LFBIS-Nummer
                  </label>
                  <input
                    type="text"
                    className="form-control"
                    id="lfbis"
                    name="lfbis"
                    value={formData.lfbis}
                    onChange={handleInputChange}
                    placeholder="z.B. AT123456789"
                    required
                  />
                </div>
                <div className="mb-3">
                  <label htmlFor="email" className="form-label">
                    <i className="bi bi-envelope me-2"></i>
                    E-Mail-Adresse
                  </label>
                  <input
                    type="email"
                    className="form-control"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    placeholder="<EMAIL>"
                    required
                  />
                </div>
                <input type="hidden" name="token" value={formData.token} />
                <button type="submit" className="btn btn-primary w-100">
                  <i className="bi bi-download me-2"></i>
                  Download starten
                </button>
              </form>

              {message.show && (
                <div className={`alert alert-${message.type} mt-3`} role="alert">
                  <i className={`bi bi-${message.type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2`}></i>
                  {message.text}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
}
