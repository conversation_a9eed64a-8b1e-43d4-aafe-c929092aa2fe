import Head from "next/head";

export default function Layout({ children, title = "ESAdok Server", description = "ELKE System für tierschutzrelevante Kontrollen in Österreich" }) {
  return (
    <>
      <Head>
        <title>{title}</title>
        <meta name="description" content={description} />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
      </Head>
      
      <div className="d-flex flex-column min-vh-100 bg-light">
        {/* Navigation */}
        <nav className="navbar navbar-expand-lg navbar-dark bg-primary shadow-sm">
          <div className="container">
            <a href="/" className="navbar-brand mb-0 h1 text-decoration-none">
              <i className="bi bi-file-earmark-pdf me-2"></i>
              ESAdok Server
            </a>
            <div className="navbar-nav ms-auto">
              <a href="/" className="nav-link text-light">
                <i className="bi bi-house me-1"></i>
                Home
              </a>
            </div>
          </div>
        </nav>

        {/* Main Content - Flex-grow sorgt dafür, dass der Content den verfügbaren Platz einnimmt */}
        <main className="flex-grow-1 d-flex align-items-center">
          <div className="container-fluid py-4 w-100">
            {children}
          </div>
        </main>

        {/* Footer - Bleibt immer am unteren Ende */}
        <footer className="bg-dark text-light py-3">
          <div className="container text-center">
            <small>
              © 2025 ELKE - ESAdok Server - <a
                href="https://www.esculenta.at"
                target="_blank"
                rel="noopener noreferrer"
                className="text-light text-decoration-none"
                style={{ borderBottom: '1px dotted rgba(255,255,255,0.5)' }}
              >
                EsCulenta GmbH
              </a>
            </small>
          </div>
        </footer>
      </div>
    </>
  );
}
