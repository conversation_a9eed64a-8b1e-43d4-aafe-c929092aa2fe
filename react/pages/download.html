<!DOCTYPE html>
<html lang="de">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>PDF-Download – ESAdok</title>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css"/>
  <style>
    body {
      background-color: #f8f9fa;
    }
    .download-box {
      max-width: 500px;
      margin: 5rem auto;
      padding: 2rem;
      background-color: white;
      border-radius: 0.5rem;
      box-shadow: 0 0 20px rgba(0,0,0,0.1);
    }
  </style>
</head>
<body>

<div class="download-box">
  <h4 class="mb-3 text-center">Kontrollbericht herunterladen</h4>
  <p class="text-muted text-center">Bitte geben Sie Ihre Daten e<PERSON>, um das Dokument abzurufen.</p>

  <form id="downloadForm">
    <div class="mb-3">
      <label for="lfbis" class="form-label">LFBIS-Nummer</label>
      <input type="text" class="form-control" id="lfbis" required>
    </div>
    <div class="mb-3">
      <label for="email" class="form-label">E-Mail-Adresse</label>
      <input type="email" class="form-control" id="email" required>
    </div>
    <input type="hidden" id="token" value="">
    <button type="submit" class="btn btn-primary w-100">Download starten</button>
  </form>

  <div id="message" class="alert mt-3 d-none" role="alert"></div>
</div>

<script>
  // Token aus URL lesen
  const urlParams = new URLSearchParams(window.location.search);
  const token = urlParams.get('token');
  if (token) document.getElementById('token').value = token;

  const form = document.getElementById('downloadForm');
  const message = document.getElementById('message');

  form.addEventListener('submit', async (e) => {
    e.preventDefault();
    message.classList.add('d-none');

    const data = {
      token: document.getElementById('token').value,
      lfbis: document.getElementById('lfbis').value,
      email: document.getElementById('email').value
    };

    try {
      const response = await fetch('/api/verify-download', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      });

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'Kontrollbericht.pdf';
        document.body.appendChild(a);
        a.click();
        a.remove();
      } else {
        const err = await response.json();
        showMessage('danger', err.message || 'Zugriff verweigert.');
      }
    } catch (err) {
      showMessage('danger', 'Fehler beim Serverzugriff.');
    }
  });

  function showMessage(type, text) {
    message.className = `alert alert-${type} mt-3`;
    message.textContent = text;
    message.classList.remove('d-none');
  }
</script>

</body>
</html>
