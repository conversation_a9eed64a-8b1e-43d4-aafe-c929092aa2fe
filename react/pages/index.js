import Layout from "./components/Layout";

export default function Home() {
  return (
    <Layout title="ESAdok Server - Home" description="ESAdok PDF Server - ELKE System für tierschutzrelevante Kontrollen in Österreich">
      <div className="row justify-content-center align-items-center" style={{ minHeight: 'calc(100vh - 200px)' }}>
        <div className="col-md-8 col-lg-6">
          <div className="card shadow-lg border-0">
            <div className="card-body text-center p-5">
              <div className="mb-4">
                <i className="bi bi-server text-primary" style={{ fontSize: '4rem' }}></i>
              </div>

              <h1 className="card-title display-4 fw-bold text-primary mb-3">
                ESAdok Server
              </h1>

              <p className="card-text lead text-muted mb-4">
                ELKE System für tierschutzrelevante Kontrollen in Österreich
              </p>

              <div className="row text-center mt-4">
                <div className="col-md-4 mb-3">
                  <div className="p-3">
                    <i className="bi bi-file-earmark-pdf text-success" style={{ fontSize: '2rem' }}></i>
                    <h6 className="mt-2 text-muted">PDF Management</h6>
                  </div>
                </div>
                <div className="col-md-4 mb-3">
                  <div className="p-3">
                    <i className="bi bi-shield-check text-info" style={{ fontSize: '2rem' }}></i>
                    <h6 className="mt-2 text-muted">Tierschutzkontrollen</h6>
                  </div>
                </div>
                <div className="col-md-4 mb-3">
                  <div className="p-3">
                    <i className="bi bi-database text-warning" style={{ fontSize: '2rem' }}></i>
                    <h6 className="mt-2 text-muted">Secure Storage</h6>
                  </div>
                </div>
              </div>

              <div className="mt-4 pt-3 border-top">
                <small className="text-muted">
                  <i className="bi bi-info-circle me-1"></i>
                  Server läuft und bereit für API-Anfragen
                </small>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
}
